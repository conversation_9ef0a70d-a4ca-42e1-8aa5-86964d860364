import ffmpeg from "fluent-ffmpeg";
import ffmpegStatic from "ffmpeg-static";
import fs from "fs";
import path from "path";
import ffprobe from "@ffprobe-installer/ffprobe";
import jwt, { Algorithm } from "jsonwebtoken";
import jwksClient from "jwks-rsa";

import { AWS_REGION, HLS_BUCKET, JWT_SECRET, LIVE2_HTTP_PORT } from "../config/environment";
import { WEIGHTS } from "../constants/trendingWeights";
import { Response } from "express";
import axios from "axios";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
// Removed parseString import - no longer needed after debug code cleanup

const s3Client = new S3Client({
  region: AWS_REGION,
});
const APPLE_BASE_URL = "https://appleid.apple.com";
const JWKS_APPLE_URI = "/auth/keys";

/**
 * We Accept createdAt and return "Days ago"
 * @param createdAt
 * @returns Number
 */
export function calculateDaysAgo(createdAt: Date): number {
  const createdDate = new Date(createdAt);
  const currentDate = new Date();
  return Math.floor(
    (currentDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
  );
}

ffmpeg.setFfmpegPath(ffmpegStatic!);
ffmpeg.setFfprobePath(ffprobe.path);

/**
 * Following function uses ffmpeg to retrieve Video Metadata.
 * @param buffer
 * @returns
 */
export const getVideoMetadataFromBuffer = async (
  buffer: any
): Promise<number> => {
  return new Promise((resolve, reject) => {
    const tempDir = path.join(__dirname, "tmp");
    const tempFilePath = path.join(tempDir, `${Date.now()}.mp4`);

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    fs.writeFileSync(tempFilePath, buffer);

    ffmpeg.ffprobe(tempFilePath, (err: any, metadata: any) => {
      fs.unlinkSync(tempFilePath);
      if (err) {
        return reject(err);
      }
      resolve(metadata.format.duration);
    });
  });
};

/**
 * Generates a JWT token
 * @param payload - The data to encode in the token
 * @param expiresIn - The token's expiration time (default is '1h')
 * @returns The signed JWT token
 */
export const generateToken = (payload: object, expiresIn: string) => {
  if (!JWT_SECRET) {
    throw new Error("JWT_SECRET is not defined");
  }
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

/**
 * We want to calculate base score with the help of weights. Weights can be adjusted later accordingly.
 * Decay factor makes sure new content gets the boost by implementing exponential decay.
 * At last we combine those two.
 * Base score: we measure the engagement and decay factor:Adjusts this score based on time
 * @param views We accept view count.
 * @param likes like count.
 * @param shares share count.
 * @param createdAt post date.
 * @returns {number} trending score for a stream.
 */
export const calculateTrendingScore = async (
  views: number,
  likes: number,
  shares: number,
  createdAt: Date
): Promise<number> => {
  const baseScore =
    WEIGHTS.VIEWS * views + WEIGHTS.LIKES * likes + WEIGHTS.SHARES * shares;
  const timeSincePosted =
    (Date.now() - new Date(createdAt).getTime()) / 3600000;
  const decayFactor = Math.exp(-timeSincePosted / WEIGHTS.DECAY_PERIOD);
  const trendingScore = baseScore * decayFactor;
  return trendingScore;
};

export const validateAppleToken = async (identityToken: string) => {
  const decodedToken = jwt.decode(identityToken, { complete: true });

  if (!decodedToken) throw new Error("Invalid identity token");

  const { kid, alg } = decodedToken.header;

  const client = jwksClient({ jwksUri: `${APPLE_BASE_URL}${JWKS_APPLE_URI}` });

  const signingKey = await client.getSigningKey(kid);

  const publicKey = signingKey.getPublicKey();

  const verifiedToken = jwt.verify(identityToken, publicKey, {
    algorithms: [alg as Algorithm],
  });

  if (typeof verifiedToken !== "object" || verifiedToken === null) {
    throw new Error("Invalid token payload");
  }

  if (verifiedToken.iss !== APPLE_BASE_URL) {
    throw new Error("Invalid issuer");
  }

  return verifiedToken;
};

export interface ApiResponse<T> {
  statusCode: number;
  success: boolean;
  message: string;
  data: T | null;
}
export const ResultDB = <T>(
  statusCode: number,
  success: boolean,
  message: string,
  data: T | null = null
): ApiResponse<T> => {
  return { statusCode, success, message, data };
};

export interface ResponseType {
  success: boolean;
  message: string;
  data: any;
}
export const SuccessResponse = <T>(
  res: Response,
  statusCode: number = 200,
  success: boolean,
  message: string,
  data: T | null = null
): Response<ResponseType> => {
  return res.status(statusCode).json({
    success: success,
    message,
    data,
  });
};

export const ErrorResponse = <T>(
  res: Response,
  statusCode: number = 500,
  success: boolean,
  message: string,
  data: T | null = null
): Response<ResponseType> => {
  return res.status(statusCode).json({
    success: success,
    message,
    data,
  });
};

/**
 * Calculate the total length of a video from an M3U8 playlist
 * This function is non-blocking and won't prevent stream operations from succeeding
 * For LLHLS streams, it tries both LLHLS URLs and S3 URLs as fallback
 * @param streamKey The stream key to calculate the video length for
 * @returns The total duration of the video in seconds, or null if unavailable
 */
export const calculateVideoLengthFromM3U8 = async (
  streamKey: string
): Promise<number | null> => {
  // Make this function completely non-blocking - run in background
  setImmediate(async () => {
    try {
      // For LLHLS streams, try the LLHLS URL first (direct from OvenMediaEngine)
      const llhlsUrl = `http://localhost:${LIVE2_HTTP_PORT}/app/${streamKey}/llhls.m3u8`;

      console.log(`[Background] Attempting to calculate video length from LLHLS: ${llhlsUrl}`);

      // Add retry logic with exponential backoff for cases where stream might not be ready yet
      let retries = 3;
      let delay = 2000; // Start with 2 second delay to allow stream to stabilize

      // Try LLHLS URL first, then fallback to S3 URL
      const urlsToTry = [
        llhlsUrl,
        `https://s3.${AWS_REGION}.amazonaws.com/${HLS_BUCKET}/hls/${streamKey}_1080/index.m3u8`
      ];

      for (const playlistUrl of urlsToTry) {
        let currentRetries = retries;
        let currentDelay = delay;

        while (currentRetries > 0) {
          try {
            console.log(`[Background] Trying URL: ${playlistUrl}`);

            // 1. Fetch the M3U8 playlist
            const response = await axios.get(playlistUrl, {
            timeout: 15000, // 15 second timeout
            headers: {
              'Accept': 'application/vnd.apple.mpegurl'
            }
          });

          const playlistContent = response.data;

          // 2. Parse the M3U8 playlist to sum up the durations
          const durationRegex = /#EXTINF:([\d.]+)/g;
          let match;
          let totalDuration = 0;

          while ((match = durationRegex.exec(playlistContent)) !== null) {
            totalDuration += parseFloat(match[1]);
          }

          console.log(`[Background] Successfully calculated video length: ${totalDuration} seconds for stream ${streamKey}`);

          // TODO: Update stream record with video length if needed
          // This could be used for analytics or VOD metadata

          return totalDuration;
        } catch (retryError: any) {
          currentRetries--;
          if (currentRetries === 0) {
            console.log(`[Background] All retries failed for URL ${playlistUrl}, trying next URL if available`);
            break; // Try next URL
          }

          console.log(`[Background] Retry ${3 - currentRetries} failed for stream ${streamKey} with URL ${playlistUrl}, retrying in ${currentDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, currentDelay));
          currentDelay *= 2; // Exponential backoff
        }
      }
    }
    } catch (error: any) {
      console.warn("[Background] Could not calculate video length (non-critical):", {
        streamKey,
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText
      });
    }
  });

  // Return null immediately - this operation is now non-blocking
  console.log(`[Non-blocking] Video length calculation queued for stream ${streamKey}`);
  return null;
};

/**
 * Following function appends #EXT-X-ENDLIST to the end of the M3U8 playlist
 * Simplified to handle only 1080p quality as per system optimization
 * This function is non-blocking and won't prevent stream operations from succeeding
 * @param streamKey
 */
export const appendEndlistToM3U8 = async (streamKey: string) => {
  // Make this function completely non-blocking - run in background
  setImmediate(async () => {
    // For LLHLS streams, try the LLHLS URL first, then fallback to S3
    const llhlsUrl = `http://localhost:${LIVE2_HTTP_PORT}/app/${streamKey}/llhls.m3u8`;
    const s3Url = `https://s3.${AWS_REGION}.amazonaws.com/${HLS_BUCKET}/hls/${streamKey}_1080/index.m3u8`;

    const urlsToTry = [llhlsUrl, s3Url];

    for (const variantUrl of urlsToTry) {
      try {
        console.log("[Background] Processing variant url:", variantUrl);

        // Add retry logic with exponential backoff
        let retries = 3;
        let delay = 3000; // Start with 3 second delay for file upload completion

        while (retries > 0) {
          try {
            // Download the current playlist
            const response = await axios.get(variantUrl, {
            timeout: 15000, // 15 second timeout
            headers: {
              'Accept': 'application/vnd.apple.mpegurl'
            }
          });

          let playlistContent = response.data;

          // Check if `#EXT-X-ENDLIST` is already present
          if (!playlistContent.includes("#EXT-X-ENDLIST")) {
            playlistContent += "\n#EXT-X-ENDLIST\n";

            // Upload updated playlist back to S3
            const s3Key = `${HLS_BUCKET}/hls/${streamKey}_1080/index.m3u8`;

            console.log("[Background] Updating playlist with EXT-X-ENDLIST, s3Key:", s3Key);
            const command = new PutObjectCommand({
              Bucket: HLS_BUCKET,
              Key: `hls/${streamKey}_1080/index.m3u8`,
              Body: playlistContent,
              ContentType: "application/vnd.apple.mpegurl",
            });

            const responseS3 = await s3Client.send(command);
            console.log("[Background] Successfully updated playlist with EXT-X-ENDLIST:", responseS3.ETag);
          } else {
            console.log("[Background] EXT-X-ENDLIST already present in playlist");
          }

          return; // Success, exit the function
        } catch (retryError: any) {
          retries--;
          if (retries === 0) {
            throw retryError;
          }

          console.log(`[Background] Retry ${3 - retries} failed for appendEndlistToM3U8, retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2; // Exponential backoff
        }
      }
    } catch (error: any) {
      console.warn("[Background] Could not append EXT-X-ENDLIST (non-critical):", {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        streamKey
      });
    }
    }
  });

  // Return immediately - this operation is now non-blocking
  console.log(`[Non-blocking] EXT-X-ENDLIST append queued for stream ${streamKey}`);
};

// Removed commented debug code for production deployment
//     console.error("Stats polling failed:", e);
//   }
// }); // every 5 seconds
