import { NextFunction, Request, Response } from "express";
import { UserService } from "../services";
import { STATUS_CODES } from "../constants/statusCodes";
import logger from "../config/logger";
import { LLHLS_WEBHOOK_SECRET } from "../config/environment";

export const authenticateStartStream = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.info("Start stream authentication request:", { body: req.body });
  const { name } = req.body;

  if (!name) {
    return res
      .status(STATUS_CODES.BAD_REQUEST)
      .send("Unauthorized: name field missing");
  }

  try {
    const [userId, liveStreamId] = name.split("_");
    const user = await UserService.getUser(userId);
    logger.info("User found for stream start:", { userId, username: user?.username });
    if (!user) {
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .send("Unauthorized: User not found");
    }

    req.user = user;
    next();
  } catch (err) {
    logger.error("Start stream verification error:", err);
    return res
      .status(STATUS_CODES.UNAUTHORIZED)
      .send("Unauthorized: Invalid User");
  }
};

export const authenticateEndStream = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.info("End stream authentication request:", { body: req.body });
  const { name } = req.body;

  if (!name) {
    return res
      .status(STATUS_CODES.BAD_REQUEST)
      .send("Unauthorized: name field missing");
  }

  try {
    const [userId, liveStreamId] = name.split("_");
    const user = await UserService.getUser(userId);
    logger.info("User found for stream end:", { userId, username: user?.username });
    if (!user) {
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .send("Unauthorized: User not found");
    }

    req.user = user;

    next();
  } catch (err) {
    logger.error("End stream token verification error:", err);
    return res
      .status(STATUS_CODES.UNAUTHORIZED)
      .send("Unauthorized: Invalid user");
  }
};

/**
 * Authentication middleware for LLHLS (OvenMediaEngine) AdmissionWebhooks
 * Validates the HMAC-SHA1 signature from OvenMediaEngine
 */
export const authenticateLLHLSWebhook = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.info("LLHLS webhook authentication request:", {
    body: req.body,
    headers: req.headers
  });

  const expectedSecret = LLHLS_WEBHOOK_SECRET;

  try {
    // Get the signature from OvenMediaEngine
    const providedSignature = req.headers['x-ome-signature'] as string;

    if (!providedSignature) {
      logger.warn("LLHLS webhook missing secret key");
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .json({ success: false, message: "Missing authentication secret" });
    }

    // Verify HMAC-SHA1 signature
    const crypto = require('crypto');
    const payload = JSON.stringify(req.body);
    const expectedSignature = crypto
      .createHmac('sha1', expectedSecret)
      .update(payload)
      .digest('base64');

    // Convert URL-safe Base64 to standard Base64 for comparison
    const normalizeBase64 = (sig: string) => {
      return sig.replace(/-/g, '+').replace(/_/g, '/') + '='.repeat((4 - sig.length % 4) % 4);
    };

    const normalizedProvided = normalizeBase64(providedSignature);
    const normalizedExpected = expectedSignature;

    if (normalizedProvided !== normalizedExpected) {
      logger.warn("LLHLS webhook invalid signature:", {
        provided: providedSignature,
        normalizedProvided: normalizedProvided,
        expected: expectedSignature
      });
      return res
        .status(STATUS_CODES.UNAUTHORIZED)
        .json({ success: false, message: "Invalid authentication signature" });
    }

    // Validate required webhook fields
    const { request } = req.body;
    if (!request || !request.url) {
      logger.warn("LLHLS webhook missing required fields:", { body: req.body });
      return res
        .status(STATUS_CODES.BAD_REQUEST)
        .json({ success: false, message: "Invalid webhook payload" });
    }

    logger.info("LLHLS webhook authenticated successfully");
    next();
  } catch (err) {
    logger.error("LLHLS webhook authentication error:", err);
    return res
      .status(STATUS_CODES.UNAUTHORIZED)
      .json({ success: false, message: "Authentication failed" });
  }
};
