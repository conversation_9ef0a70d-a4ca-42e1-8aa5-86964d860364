<?xml version="1.0" encoding="UTF-8"?>

<Server version="8">
	<Name>OvenMediaEngine</Name>
	<!-- Host type (origin/edge) -->
	<Type>origin</Type>
	
	<IP>*</IP>
	<PrivacyProtection>false</PrivacyProtection>
	<StunServer>stun.ovenmediaengine.com:13478</StunServer>

	<Modules>
		<!-- 
		Currently OME only supports h2 like all browsers do. Therefore, HTTP/2 only works on TLS ports.			
		-->
		<HTTP2>
			<Enable>true</Enable>
		</HTTP2>

		<LLHLS>
			<Enable>true</Enable>
		</LLHLS>

		<!-- P2P works only in WebRTC and is experiment feature -->
		<P2P>
			<!-- disabled by default -->
			<Enable>false</Enable>
			<MaxClientPeersPerHostPeer>2</MaxClientPeersPerHostPeer>
		</P2P>
	</Modules>

	<!-- Settings for the ports to bind -->
	<Bind>
		<!-- Enable this configuration if you want to use API Server -->
		<!--
		<Managers>
			<API>
				<Port>8081</Port>
				<TLSPort>8082</TLSPort>
				<WorkerCount>1</WorkerCount>
			</API>
		</Managers>
		-->

		<Providers>
			<!-- Pull providers -->
			<RTSPC>
				<WorkerCount>1</WorkerCount>
			</RTSPC>
			<!-- OVT provider commented out for LLHLS optimization -->
			<!--
			<OVT>
				<WorkerCount>1</WorkerCount>
			</OVT>
			-->
			<!-- Push providers -->
			<RTMP>
				<Port>1935</Port>
				<!-- Increased worker count for better RTMP handling -->
				<WorkerCount>4</WorkerCount>
			</RTMP>
			<!-- SRT provider commented out for LLHLS optimization -->
			<!--
			<SRT>
				<Port>9999</Port>
				<WorkerCount>1</WorkerCount>
			</SRT>
			-->
			<!-- MPEGTS provider commented out for LLHLS optimization -->
			<!--
			<MPEGTS>
				<Port>4000/udp</Port>
			</MPEGTS>
			-->
			<WebRTC>
				<Signalling>
					<Port>3333</Port>
					<TLSPort>3334</TLSPort>
					<WorkerCount>1</WorkerCount>
				</Signalling>

				<IceCandidates>
					<!-- Uncomment the line below to use IPv6 ICE Candidate -->
					<!-- <IceCandidate>[::]:10000-10004/udp</IceCandidate> -->
					<IceCandidate>*:10000-10004/udp</IceCandidate>

					<!-- Uncomment the line below to use a link local address when specifying an address with IPv6 wildcard (::) -->
					<!-- <EnableLinkLocalAddress>true</EnableLinkLocalAddress> -->

					<!-- 
						If you want to stream WebRTC over TCP, specify IP:Port for TURN server.
						This uses the TURN protocol, which delivers the stream from the built-in TURN server to the player's TURN client over TCP. 
					-->
					<TcpRelay>*:3478</TcpRelay>
					<!-- TcpForce is an option to force the use of TCP rather than UDP in WebRTC streaming. (You can omit ?transport=tcp accordingly.) If <TcpRelay> is not set, playback may fail. -->
					<TcpForce>true</TcpForce>
					<TcpRelayWorkerCount>1</TcpRelayWorkerCount>
				</IceCandidates>
			</WebRTC>
		</Providers>

		<Publishers>
			<!-- OVT publisher commented out for LLHLS optimization -->
			<!--
			<OVT>
				<Port>9000</Port>
				<WorkerCount>1</WorkerCount>
			</OVT>
			-->
			<LLHLS>
				<!--
				OME only supports h2, so LLHLS works over HTTP/1.1 on non-TLS ports.
				LLHLS works with higher performance over HTTP/2,
				so it is recommended to use a TLS port.
				Port changed to 8080 for dedicated LLHLS streaming.
				-->
				<Port>8080</Port>
				<!-- If you want to use TLS, specify the TLS port -->
				<TLSPort>8443</TLSPort>
				<!-- Increased worker count for better LLHLS performance -->
				<WorkerCount>4</WorkerCount>
			</LLHLS>
			<WebRTC>
				<Signalling>
					<Port>3333</Port>
					<TLSPort>3334</TLSPort>
					<WorkerCount>1</WorkerCount>
				</Signalling>
				<IceCandidates>
					<!-- Uncomment the line below to use IPv6 ICE Candidate -->
					<!-- <IceCandidate>[::]:10000-10004/udp</IceCandidate> -->
					<IceCandidate>*:10000-10004/udp</IceCandidate>
					
					<!-- Uncomment the line below to use a link local address when specifying an address with IPv6 wildcard (::) -->
					<!-- <EnableLinkLocalAddress>true</EnableLinkLocalAddress> -->

					<!-- 
						If you want to stream WebRTC over TCP, specify IP:Port for TURN server.
						This uses the TURN protocol, which delivers the stream from the built-in TURN server to the player's TURN client over TCP. 
					-->
					<TcpRelay>*:3478</TcpRelay>
					<!-- TcpForce is an option to force the use of TCP rather than UDP in WebRTC streaming. (You can omit ?transport=tcp accordingly.) If <TcpRelay> is not set, playback may fail. -->
					<TcpForce>true</TcpForce>
					<TcpRelayWorkerCount>1</TcpRelayWorkerCount>
				</IceCandidates>
			</WebRTC>
			<!-- SRT publisher commented out for LLHLS optimization -->
			<!--
			<SRT>
				<Port>9998</Port>
				<WorkerCount>1</WorkerCount>
			</SRT>
			-->
		</Publishers>
	</Bind>
	<VirtualHosts>
		<!-- You can use wildcard like this to include multiple XMLs -->
		<VirtualHost include="VHost*.xml" />
		<VirtualHost>
			<Name>default</Name>
			<!--Distribution is a value that can be used when grouping the same vhost distributed across multiple servers. This value is output to the events log, so you can use it to aggregate statistics. -->
			<Distribution>ovenmediaengine.com</Distribution>

			<!-- Settings for multi ip/domain and TLS -->
			<Host>
				<Names>
					<!-- Host names
						<Name>stream1.airensoft.com</Name>
						<Name>stream2.airensoft.com</Name>
						<Name>*.sub.airensoft.com</Name>
						<Name>***********</Name>
					-->
					<Name>*</Name>
				</Names>
				<!--
				<TLS>
					<CertPath>path/to/file.crt</CertPath>
					<KeyPath>path/to/file.key</KeyPath>
					<ChainCertPath>path/to/file.crt</ChainCertPath>
				</TLS>
				-->
			</Host>

	
			<AdmissionWebhooks>
				<ControlServerUrl>http://**********:8081/v1/api/stream/live</ControlServerUrl>
				<SecretKey>ome-webhook-secret-2024</SecretKey>
				<!-- Reduced timeout for faster stream lifecycle management -->
				<Timeout>5000</Timeout>
				<Enables>
					<Providers>rtmp</Providers>
					<!-- Publishers disabled to avoid double webhook calls -->
					<!-- <Publishers>rtmp</Publishers> -->
				</Enables>
			</AdmissionWebhooks>

		

			<!-- Default CORS Settings -->
			<CrossDomains>
				<Url>*</Url>
			</CrossDomains>

			<!-- Settings for applications -->
			<Applications>
				<Application>
					<Name>app</Name>
					<!-- Application type (live/vod) -->
					<Type>live</Type>
					<OutputProfiles>
						<!-- Common setting for decoders. Decodes is optional. -->
						<Decodes>
							<!-- Number of threads for the decoder. -->
							<ThreadCount>3</ThreadCount>
							<!--
							By default, OME decodes all video frames.
							With OnlyKeyframes, only keyframes are decoded, massively improving performance.
							Thumbnails are generated only on keyframes, they may not generate at your requested fps!
							-->
							<OnlyKeyframes>false</OnlyKeyframes>
						</Decodes>

						<!-- Hardware acceleration enabled for better performance and lower latency -->
						<HWAccels>
							<Decoder>
									<!-- Enable hardware decoding if available -->
									<Enable>true</Enable>
								
									<Modules>nv:0,qsv:0</Modules>
							</Decoder>
							<Encoder>
									<!-- Enable hardware encoding if available -->
									<Enable>true</Enable>
									<Modules>nv:0,qsv:0</Modules>
							</Encoder>
						</HWAccels>

						<!-- ACTIVE PROFILE: 1080p for production streaming -->
						<OutputProfile>
							<Name>1080p_profile</Name>
							<OutputStreamName>${OriginStreamName}_1080p</OutputStreamName>

							<Playlist>
								<Name>1080p</Name>
								<FileName>master_1080p</FileName>
								<Options>
									<WebRtcAutoAbr>true</WebRtcAutoAbr>
									<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
									<EnableTsPackaging>true</EnableTsPackaging>
								</Options>
								<RenditionTemplate>
									<Name>1080p</Name>
									<VideoTemplate>
										<EncodingType>encoded</EncodingType>
									</VideoTemplate>
									<AudioTemplate>
										<EncodingType>encoded</EncodingType>
									</AudioTemplate>
								</RenditionTemplate>
							</Playlist>

							<Encodes>
								<Video>
									<Name>video_1080p</Name>
									<Codec>h264</Codec>
									<Bitrate>5000000</Bitrate>
									<Width>1920</Width>
									<Height>1080</Height>
									<Framerate>30</Framerate>
									<KeyFrameInterval>30</KeyFrameInterval>
									<BFrames>0</BFrames>
									<Preset>faster</Preset>
								</Video>
								<Audio>
									<Name>aac_audio_1080p</Name>
									<Codec>aac</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
								</Audio>
							</Encodes>
						</OutputProfile>

					<!-- FUTURE PROFILE: 2K for high-quality streaming (commented out for easy activation) -->
					
					<OutputProfile>
						<Name>2k_profile</Name>
						<OutputStreamName>${OriginStreamName}_2k</OutputStreamName>

						<Playlist>
							<Name>2k</Name>
							<FileName>master_2k</FileName>
							<Options>
								<WebRtcAutoAbr>true</WebRtcAutoAbr>
								<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
								<EnableTsPackaging>true</EnableTsPackaging>
							</Options>
							<RenditionTemplate>
								<Name>2k</Name>
								<VideoTemplate>
									<EncodingType>encoded</EncodingType>
								</VideoTemplate>
								<AudioTemplate>
									<EncodingType>encoded</EncodingType>
								</AudioTemplate>
							</RenditionTemplate>
						</Playlist>

						<Encodes>
							<Video>
								<Name>video_2k</Name>
								<Codec>h264</Codec>
								<Bitrate>8000000</Bitrate>
								<Width>2560</Width>
								<Height>1440</Height>
								<Framerate>30</Framerate>
								<KeyFrameInterval>30</KeyFrameInterval>
								<BFrames>0</BFrames>
								<Preset>faster</Preset>
							</Video>
							<Audio>
								<Name>aac_audio_2k</Name>
								<Codec>aac</Codec>
								<Bitrate>128000</Bitrate>
								<Samplerate>48000</Samplerate>
								<Channel>2</Channel>
							</Audio>
						</Encodes>
					</OutputProfile>
					

					<!-- FUTURE PROFILE: 4K for ultra-high-quality streaming (commented out for easy activation) -->
					<!--
					<OutputProfile>
						<Name>4k_profile</Name>
						<OutputStreamName>${OriginStreamName}_4k</OutputStreamName>

						<Playlist>
							<Name>4k</Name>
							<FileName>master_4k</FileName>
							<Options>
								<WebRtcAutoAbr>true</WebRtcAutoAbr>
								<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
								<EnableTsPackaging>true</EnableTsPackaging>
							</Options>
							<RenditionTemplate>
								<Name>4k</Name>
								<VideoTemplate>
									<EncodingType>encoded</EncodingType>
								</VideoTemplate>
								<AudioTemplate>
									<EncodingType>encoded</EncodingType>
								</AudioTemplate>
							</RenditionTemplate>
						</Playlist>

						<Encodes>
							<Video>
								<Name>video_4k</Name>
								<Codec>h264</Codec>
								<Bitrate>15000000</Bitrate>
								<Width>2160</Width>
								<Height>3840</Height>
								<Framerate>30</Framerate>
								<KeyFrameInterval>30</KeyFrameInterval>
								<BFrames>0</BFrames>
								<Preset>faster</Preset>
							</Video>
							<Audio>
								<Name>aac_audio_4k</Name>
								<Codec>aac</Codec>
								<Bitrate>128000</Bitrate>
								<Samplerate>48000</Samplerate>
								<Channel>2</Channel>
							</Audio>
						</Encodes>
					</OutputProfile>
					-->

					<!-- BYPASS PROFILE: For when input stream already matches desired quality -->
					<OutputProfile>
							<Name>bypass_stream</Name>
							<OutputStreamName>${OriginStreamName}</OutputStreamName>

							<Playlist>
								<Name>default</Name>
								<FileName>master</FileName>
								<Options>
									<WebRtcAutoAbr>true</WebRtcAutoAbr>
									<HLSChunklistPathDepth>0</HLSChunklistPathDepth>
									<EnableTsPackaging>true</EnableTsPackaging>
								</Options>
								<RenditionTemplate>
									<Name>${Height}p</Name>
									<VideoTemplate>
										<EncodingType>all</EncodingType>
									</VideoTemplate>
									<AudioTemplate>
										<EncodingType>all</EncodingType>
									</AudioTemplate>
								</RenditionTemplate>
							</Playlist>

							<Encodes>
								<Video>
									<Name>bypass_video</Name>
									<Bypass>true</Bypass>
								</Video>
								<Audio>
									<Name>aac_audio</Name>
									<Codec>aac</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
									<BypassIfMatch>
										<Codec>eq</Codec>
									</BypassIfMatch>
								</Audio>
								<Audio>
									<Name>opus_audio</Name>
									<Codec>opus</Codec>
									<Bitrate>128000</Bitrate>
									<Samplerate>48000</Samplerate>
									<Channel>2</Channel>
									<BypassIfMatch>
										<Codec>eq</Codec>
									</BypassIfMatch>
								</Audio>
								
							</Encodes>
						</OutputProfile>
					</OutputProfiles>
					<Providers>
						<!-- OVT provider commented out for LLHLS optimization -->
						<!-- <OVT /> -->
						<WebRTC />
						<RTMP />
						
						<RTSPPull />
						<!-- WebRTC provider removed for mobile-only deployment -->

						<Schedule>
							<MediaRootDir>/opt/ovenmediaengine/media</MediaRootDir>
							<ScheduleFilesDir>/opt/ovenmediaengine/media</ScheduleFilesDir>
						</Schedule>
					</Providers>
					<Publishers>
						<!-- Increased worker counts for better performance -->
						<AppWorkerCount>2</AppWorkerCount>
						<StreamWorkerCount>12</StreamWorkerCount>
						<!-- OVT publisher commented out for LLHLS optimization -->
						<!-- <OVT /> -->
						<!-- WebRTC publisher removed for mobile-only deployment -->
						<!-- Mobile apps use LLHLS directly, no WebRTC fallback needed -->
						<LLHLS>
							<!-- Mobile-optimized ultra-low latency configuration -->
							<!-- Optimized chunk duration for mobile consumption -->
							<ChunkDuration>0.1</ChunkDuration>
							<!-- Minimal PartHoldBack for lowest possible latency -->
							<!-- SHOULD be at least three times the ChunkDuration (0.1 * 3 = 0.3) -->
							<PartHoldBack>0.3</PartHoldBack>
							<!-- Reduced segment duration for faster playlist updates -->
							<SegmentDuration>1</SegmentDuration>
							<!-- Reduced segment count for mobile - serve only latest segments to reduce buffering -->
							<SegmentCount>5</SegmentCount>
							<!-- DVR disabled for lowest latency -->
							<DVR>
								<Enable>false</Enable>
								<TempStoragePath>/tmp/ome_dvr/</TempStoragePath>
								<MaxDuration>3600</MaxDuration>
							</DVR>
							<!-- DRM disabled for performance -->
							<DRM>
								<Enable>false</Enable>
								<InfoFile>path/to/file.xml</InfoFile>
							</DRM>
							<!-- CORS headers removed for mobile-only deployment -->
							<!-- Enable default playlist generation -->
							<CreateDefaultPlaylist>true</CreateDefaultPlaylist> <!-- llhls.m3u8 -->
							<!-- Low-latency specific optimizations -->
							<!-- <BypassTranscoder>false</BypassTranscoder> -->
							<!-- <KeepAliveTimeout>30</KeepAliveTimeout> -->
						</LLHLS>
						<!-- HLS fallback removed for mobile-only deployment -->
						<!-- Mobile apps use LLHLS directly for optimal performance -->

						<!--
						<Push>
							<StreamMap>
								<Enable>false</Enable>
								<Path>path/to/file.xml</Path>
							</StreamMap>
						</Push>
						<FILE>
							<RootPath>/prefix/path/to</RootPath>
							<StreamMap>
								<Enable>false</Enable>
								<Path>path/to/file.xml</Path>
							</StreamMap>
						</FILE>
						<Thumbnail>
							<CrossDomains>
								<Url>*</Url>
							</CrossDomains>
						</Thumbnail>						
						-->
					</Publishers>
				</Application>
			</Applications>
		</VirtualHost>
	</VirtualHosts>
</Server>